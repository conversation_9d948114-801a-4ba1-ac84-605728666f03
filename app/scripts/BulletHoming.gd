extends Area2D

var Sparkle = preload("res://scenes/Sparkle.tscn")

# we can set this to true to make the bullet pass through enemies
export var isPassThrough = false

var Speed = Config.BulletSpeed * 0.7

var isDestroyed = false

var canClash = false
var doCountTowardBulletsOnScreen = true
var wasBulletRemoved = false

# Cache expensive function calls
var cached_weapon_strength_multiplier
var cached_homing_steer_force_multiplier
var cached_instance_id_str
var cached_throttle_key

# Pool reference for returning bullets to pool
var bullet_pool = null

func decreaseBulletCnt():
	if(doCountTowardBulletsOnScreen && !wasBulletRemoved):
		wasBulletRemoved = true
		Global.GameScene.BulletsOnScreen -= 1
		Global.GameScene.BulletsOnScreen = max(0, Global.GameScene.BulletsOnScreen)

func destroy(force = false):

	if isDestroyed:
		return false

	isDestroyed = true

	# if we don't force the bullet to be destroyed, we only destroy it if it's not a passthrough bullet
	if(not force):
		if(isPassThrough):
			return false

	decreaseBulletCnt()

	var sparkle = Sparkle.instance()
	sparkle.position = position
	Global.GameScene.add_child(sparkle)

	# Return to pool instead of queue_free if pool is available
	if bullet_pool != null:
		bullet_pool.return_bullet(self)
	else:
		queue_free()

func getDamagePoint():
	if(Global.GameScene.isBulletOP):
		return 1000
	return 10 * cached_weapon_strength_multiplier

func getSpeedMod():
	return 1.0

func setSpeed(speed):
	Speed = speed

var steer_force: float

var velocity = Vector2.ZERO
var acceleration = Vector2.ZERO
var target = null

var steerTimer = 0
var destroyTimer = 0
var lightParticleTimer = 0  # Timer for showing light particle
var current_time_cache = 0  # Cache current time to avoid multiple Tick.ms() calls per frame

var lifetimeMsec = 2000
var seekStartMsec = 200
var lightParticleStartMsec = 100  # Show light particle after delay

# Optimization: reduce frequency of expensive operations
var target_selection_counter = 0
var destroy_check_counter = 0

func lightParticle(isOn):
	# removed particle for now
	return false
	
	if(is_instance_valid($LightParticle)):
		$LightParticle.visible = isOn
		$LightParticle.emitting = isOn

func checkDestroy():
	if isDestroyed:
		return

	# Only check every few frames to reduce performance impact
	destroy_check_counter += 1
	if destroy_check_counter < 3:  # Check every 3rd frame
		return
	destroy_check_counter = 0

	if current_time_cache > destroyTimer:
		# don't follow after destroyTime
		lightParticle(false)
		steer_force = 0
		# self.destroy()

	if Global.isOffScreen(global_position,50):
		Global.GameScene.levelConductor.logBullet(true)
		self.destroy()

func canSteer():
	return current_time_cache > steerTimer

func checkLightParticle():
	# Show light particle after delay
	if current_time_cache > lightParticleTimer and has_node("LightParticle"):
		if not $LightParticle.visible:
			lightParticle(true)

func _ready():
	# Cache expensive function calls at initialization
	var specs = ShipSpecs.getSpecs()
	cached_weapon_strength_multiplier = specs.weapon_strength_multiplier
	cached_homing_steer_force_multiplier = specs.homing_steer_force_multiplier
	cached_instance_id_str = str(get_instance_id())
	cached_throttle_key = "HomingBullet" + cached_instance_id_str

	steer_force = 25.0 * cached_homing_steer_force_multiplier

	# Hide light particle on initialization
	if has_node("LightParticle"):
		lightParticle(false)

	Global.GameScene.levelConductor.logBullet()

func start(_target = null):

	steer_force = steer_force+(randi()%15)

	current_time_cache = Tick.ms()
	steerTimer = current_time_cache + seekStartMsec
	destroyTimer = current_time_cache + lifetimeMsec
	lightParticleTimer = current_time_cache + lightParticleStartMsec

	# Hide light particle at start
	if has_node("LightParticle"):
		lightParticle(false)

	global_transform = Transform2D()
	rotation_degrees = -90
	velocity = transform.x * Speed
	target = _target

func selectTarget():
	# Reduce target selection frequency to improve performance
	target_selection_counter += 1
	if target_selection_counter < 10:  # Only check every 10th frame
		return
	target_selection_counter = 0

	if(Global.doThrottle(cached_throttle_key, 200)):
		return

	if is_instance_valid(self.target):
		return

	var newTarget = Global.GameScene.getLevelObject().getARandomTarget(true)

	if !newTarget:
		return

	self.target = newTarget


func seek():
	var steer = Vector2.ZERO

	if is_instance_valid(target) and canSteer():
		var desired = (target.global_position - position).normalized() * Speed
		steer = (desired - velocity).normalized() * steer_force

	return steer

# Optimization: Use a simple counter instead of Timer for bullet count removal
var bullet_count_timer = 0
var bullet_count_timeout = 72  # Approximately 1.2 seconds at 60 FPS

func _process(_delta):
	# Cache current time once per frame
	current_time_cache = Tick.ms()

	# remove bullet count after some time - use frame counter instead of Timer
	if bullet_count_timer < bullet_count_timeout:
		bullet_count_timer += 1
	elif bullet_count_timer == bullet_count_timeout:
		decreaseBulletCnt()
		bullet_count_timer += 1  # Prevent multiple calls

	checkDestroy()
	checkLightParticle()
	selectTarget()

func _physics_process(delta):
	acceleration += seek()
	velocity += acceleration * delta
	velocity = velocity.clamped(Speed)
	rotation = velocity.angle()
	position += velocity * delta

# Reset method for object pooling
func reset_for_pool():
	# IMPORTANT: Clear old throttle key from Global dictionary to prevent memory leak
	if cached_throttle_key != "" and Global.throttleDic.has(cached_throttle_key):
		Global.throttleDic.erase(cached_throttle_key)

	# Reset position and transform
	position = Vector2.ZERO
	rotation = 0
	scale = Vector2.ONE
	modulate = Color(1, 1, 1, 1)

	# Reset bullet state variables
	isDestroyed = false
	wasBulletRemoved = false
	doCountTowardBulletsOnScreen = true
	isPassThrough = false
	canClash = false

	# Reset movement variables
	velocity = Vector2.ZERO
	acceleration = Vector2.ZERO
	target = null

	# Reset timers and counters
	steerTimer = 0
	destroyTimer = 0
	lightParticleTimer = 0
	current_time_cache = 0
	target_selection_counter = 0
	destroy_check_counter = 0
	bullet_count_timer = 0

	# Reset speed to default
	Speed = Config.BulletSpeed * 0.7

	# Reset particle effects - hide initially
	if has_node("LightParticle"):
		lightParticle(false)

	# Re-cache expensive function calls (they might have changed)
	var specs = ShipSpecs.getSpecs()
	cached_weapon_strength_multiplier = specs.weapon_strength_multiplier
	cached_homing_steer_force_multiplier = specs.homing_steer_force_multiplier
	cached_instance_id_str = str(get_instance_id())
	cached_throttle_key = "HomingBullet" + cached_instance_id_str

	# Reset steer force
	steer_force = 25.0 * cached_homing_steer_force_multiplier

# Set the pool reference (called by pool when creating bullets)
func set_bullet_pool(pool):
	bullet_pool = pool

# Generic Bullet Pool for all bullet types
# Provides optimized bullet management with proper state reset

extends Node

class_name BulletPool

# Pool configuration
var scene_resource
var parent_node
var available_objects = []
var active_objects = []
var max_pool_size = 150
var initial_pool_size = 20

# Initialize the bullet pool
func initialize(scene, parent, initial_size = 20, max_size = 150):
	scene_resource = scene
	parent_node = parent
	initial_pool_size = initial_size
	max_pool_size = max_size

	# Pre-populate the pool
	for _i in range(initial_pool_size):
		var obj = scene_resource.instance()
		obj.set_process(false)
		obj.set_physics_process(false)
		obj.visible = false
		available_objects.append(obj)

# Get an object from the pool
func get_object():
	var obj

	if available_objects.size() > 0:
		# Reuse an existing object
		obj = available_objects.pop_back()
	else:
		# Create a new object if pool is empty and we haven't hit the limit
		if active_objects.size() < max_pool_size:
			obj = scene_resource.instance()
		else:
			# Pool is at capacity, return null or oldest active object
			print("Warning: BulletPool at capacity, creating new object anyway")
			obj = scene_resource.instance()

	# Activate the object
	obj.set_process(true)
	obj.set_physics_process(true)
	obj.visible = true

	# Add to parent and track as active
	parent_node.add_child(obj)
	active_objects.append(obj)

	return obj

# Return an object to the pool
func return_object(obj):
	if obj == null or not is_instance_valid(obj):
		return

	# Remove from active tracking
	var index = active_objects.find(obj)
	if index >= 0:
		active_objects.remove(index)

	# Remove from scene tree
	if obj.get_parent():
		obj.get_parent().remove_child(obj)

	# Reset object state
	_reset_object(obj)

	# Deactivate the object
	obj.set_process(false)
	obj.set_physics_process(false)
	obj.visible = false

	# Return to available pool if we have space
	if available_objects.size() < max_pool_size:
		available_objects.append(obj)
	else:
		# Pool is full, destroy the object
		obj.queue_free()

# Reset method to properly reset bullet objects
func _reset_object(obj):
	if obj.has_method("reset_for_pool"):
		obj.reset_for_pool()
	else:
		# Fallback reset based on bullet type
		if obj.get_script() and obj.get_script().get_path().ends_with("BulletHoming.gd"):
			_reset_bullet_homing(obj)
		elif obj.get_script() and obj.get_script().get_path().ends_with("BulletLaser.gd"):
			_reset_bullet_laser(obj)
		else:
			# Default reset for basic bullets (Bullet.gd, BulletSuper.gd)
			_reset_bullet_base(obj)

# Specialized reset for BulletHoming objects
func _reset_bullet_homing(bullet):
	# IMPORTANT: Clear old throttle key from Global dictionary to prevent memory leak
	if bullet.has("cached_throttle_key") and bullet.cached_throttle_key != "" and Global.throttleDic.has(bullet.cached_throttle_key):
		Global.throttleDic.erase(bullet.cached_throttle_key)

	# Reset position and transform
	bullet.position = Vector2.ZERO
	bullet.rotation = 0
	bullet.scale = Vector2.ONE
	bullet.modulate = Color(1, 1, 1, 1)
	
	# Reset bullet state variables
	bullet.isDestroyed = false
	bullet.wasBulletRemoved = false
	bullet.doCountTowardBulletsOnScreen = true
	bullet.isPassThrough = false
	bullet.canClash = false
	
	# Reset movement variables
	bullet.velocity = Vector2.ZERO
	bullet.acceleration = Vector2.ZERO
	bullet.target = null
	
	# Reset timers and counters
	bullet.steerTimer = 0
	bullet.destroyTimer = 0
	bullet.current_time_cache = 0
	bullet.target_selection_counter = 0
	bullet.destroy_check_counter = 0
	bullet.bullet_count_timer = 0
	
	# Reset speed to default (will be properly set by bullet's reset_for_pool method)
	# bullet.Speed = Config.BulletSpeed * 0.7  # This will be handled by the bullet itself
	
	# Reset particle effects
	if bullet.has_node("LightParticle"):
		bullet.get_node("LightParticle").visible = true
		bullet.get_node("LightParticle").emitting = true
	
	# Disconnect any existing signals to prevent issues
	if bullet.is_connected("area_entered", bullet, "_on_hit"):
		bullet.disconnect("area_entered", bullet, "_on_hit")
	
	# Reconnect the hit signal
	if bullet.has_method("_on_hit"):
		var _c = bullet.connect("area_entered", bullet, "_on_hit")

# Reset method for basic bullets (Bullet.gd, BulletSuper.gd)
func _reset_bullet_base(bullet):
	# Reset position and transform
	bullet.position = Vector2.ZERO
	bullet.rotation = 0
	bullet.scale = Vector2.ONE
	bullet.modulate = Color(1, 1, 1, 1)

	# Reset bullet state variables
	bullet.isDestroyed = false
	bullet.doCountTowardBulletsOnScreen = true
	bullet.isPassThrough = false
	bullet.canClash = false

	# Reset movement variables
	bullet.velocity = Vector2(0, -1)  # Default upward movement

	# Reset speed to default
	bullet.Speed = Config.BulletSpeed

	# Reset particle effects if they exist
	if bullet.has_node("Particles2D"):
		bullet.get_node("Particles2D").visible = true
		bullet.get_node("Particles2D").emitting = true

	# Disconnect any existing signals to prevent issues
	if bullet.is_connected("area_entered", bullet, "_on_hit"):
		bullet.disconnect("area_entered", bullet, "_on_hit")

	# Reconnect the hit signal
	if bullet.has_method("_on_hit"):
		var _c = bullet.connect("area_entered", bullet, "_on_hit")

# Reset method for laser bullets (BulletLaser.gd)
func _reset_bullet_laser(bullet):
	# Reset position and transform
	bullet.position = Vector2.ZERO
	bullet.rotation = 0
	bullet.scale = Vector2.ONE
	bullet.modulate = Color(1, 1, 1, 1)

	# Reset bullet state variables
	bullet.isDestroyed = false
	bullet.doCountTowardBulletsOnScreen = true
	bullet.isPassThrough = false
	bullet.canClash = false

	# Reset laser-specific variables
	bullet.offsetY = 0

	# Reset speed to default
	bullet.Speed = Config.BulletSpeed

	# Reset laser collision
	if bullet.has_node("LaserCollision"):
		bullet.get_node("LaserCollision").disabled = false
		bullet.get_node("LaserCollision").scale = Vector2.ONE

	# Disconnect any existing signals to prevent issues
	if bullet.is_connected("area_entered", bullet, "_on_hit"):
		bullet.disconnect("area_entered", bullet, "_on_hit")

	# Reconnect the hit signal
	if bullet.has_method("_on_hit"):
		var _c = bullet.connect("area_entered", bullet, "_on_hit")

# Get a bullet from the pool and initialize it
func get_bullet(target = null):
	var bullet = get_object()
	if bullet:
		# Set pool reference so bullet can return itself (if supported)
		if bullet.has_method("set_bullet_pool"):
			bullet.set_bullet_pool(self)

		# Initialize the bullet after getting it from pool
		if bullet.has_method("start"):
			bullet.start(target)  # For homing bullets
		elif bullet.has_method("_ready"):
			# For basic bullets, call _ready to initialize
			bullet._ready()
	return bullet

# Return a bullet to the pool (called from bullet's destroy method)
func return_bullet(bullet):
	return_object(bullet)

# Get pool statistics
func get_stats():
	return {
		"available": available_objects.size(),
		"active": active_objects.size(),
		"total": available_objects.size() + active_objects.size()
	}

# Clean up the pool
func cleanup():
	# Free all available objects
	for obj in available_objects:
		if is_instance_valid(obj):
			obj.queue_free()
	available_objects.clear()

	# Note: Active objects should be returned to pool naturally
	# or will be cleaned up when their parent nodes are freed
	active_objects.clear()

func _exit_tree():
	cleanup()

# Create specialized pool instances for different bullet types
static func create_bullet_pool(parent_node_arg):
	var bullet_pool = load("res://scripts/BulletPool.gd").new()
	var bullet_scene = preload("res://scenes/Bullet.tscn")
	bullet_pool.initialize(bullet_scene, parent_node_arg, 30, 200)  # Start with 30, max 200
	return bullet_pool

static func create_bullet_super_pool(parent_node_arg):
	var bullet_pool = load("res://scripts/BulletPool.gd").new()
	var bullet_scene = preload("res://scenes/BulletSuper.tscn")
	bullet_pool.initialize(bullet_scene, parent_node_arg, 20, 150)  # Start with 20, max 150
	return bullet_pool

static func create_bullet_homing_pool(parent_node_arg):
	var bullet_pool = load("res://scripts/BulletPool.gd").new()
	var bullet_scene = preload("res://scenes/BulletHoming.tscn")
	bullet_pool.initialize(bullet_scene, parent_node_arg, 20, 150)  # Start with 20, max 150
	return bullet_pool

static func create_bullet_laser_pool(parent_node_arg):
	var bullet_pool = load("res://scripts/BulletPool.gd").new()
	var bullet_scene = preload("res://scenes/BulletLaser.tscn")
	bullet_pool.initialize(bullet_scene, parent_node_arg, 10, 50)   # Start with 10, max 50 (lasers are less frequent)
	return bullet_pool

# Legacy method for backward compatibility
static func create_global_instance(parent_node_arg):
	return create_bullet_homing_pool(parent_node_arg)

# Test script to validate CrystalPool functionality
# This script can be used to test the crystal pooling system independently

extends Node

# Test the CrystalPool system
func test_crystal_pool():
	print("=== CrystalPool Test Started ===")
	
	# Create a test pool
	var pool = CrystalPool.new()
	var crystal_scene = preload("res://scenes/CrystalRigid.tscn")
	
	# Initialize the pool
	pool.initialize(crystal_scene, self, 5, 20)
	
	print("Pool initialized with 5 initial crystals, max 20")
	print("Initial stats: ", pool.get_stats())
	
	# Test getting crystals from pool
	var crystals = []
	for i in range(10):
		var crystal = pool.get_crystal(Global.CrystalType.c10 if i % 2 == 0 else Global.CrystalType.c5)
		if crystal:
			crystals.append(crystal)
			print("Got crystal ", i, " - Pool stats: ", pool.get_stats())
		else:
			print("Failed to get crystal ", i)
	
	print("Retrieved 10 crystals")
	print("Current stats: ", pool.get_stats())
	
	# Test returning crystals to pool
	for i in range(5):
		if i < crystals.size():
			pool.return_crystal(crystals[i])
			print("Returned crystal ", i, " - Pool stats: ", pool.get_stats())
	
	print("Returned 5 crystals")
	print("Final stats: ", pool.get_stats())
	
	# Test getting crystals again (should reuse returned ones)
	for i in range(3):
		var crystal = pool.get_crystal(Global.CrystalType.c20)
		if crystal:
			print("Reused crystal ", i, " - Pool stats: ", pool.get_stats())
	
	print("=== CrystalPool Test Completed ===")

func _ready():
	# Run the test
	call_deferred("test_crystal_pool")

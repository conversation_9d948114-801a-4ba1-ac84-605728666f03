# Crystal Pool for optimized crystal management
# Provides optimized crystal spawning with proper state reset

extends Node

class_name CrystalPool

# Pool configuration
var scene_resource
var parent_node
var available_objects = []
var active_objects = []
var max_pool_size = 100
var initial_pool_size = 15

# Initialize the crystal pool
func initialize(scene, parent, initial_size = 15, max_size = 100):
	scene_resource = scene
	parent_node = parent
	initial_pool_size = initial_size
	max_pool_size = max_size

	# Pre-populate the pool
	for _i in range(initial_pool_size):
		var obj = scene_resource.instance()
		obj.set_process(false)
		obj.set_physics_process(false)
		obj.visible = false
		available_objects.append(obj)

# Get an object from the pool
func get_object():
	var obj

	if available_objects.size() > 0:
		# Reuse an existing object
		obj = available_objects.pop_back()
	else:
		# Create a new object if pool is empty and we haven't hit the limit
		if active_objects.size() < max_pool_size:
			obj = scene_resource.instance()
		else:
			# Pool is at capacity, return null or oldest active object
			print("Warning: CrystalPool at capacity, creating new object anyway")
			obj = scene_resource.instance()

	# Activate the object
	obj.set_process(true)
	obj.set_physics_process(true)
	obj.visible = true

	# Add to parent and track as active
	parent_node.add_child(obj)
	active_objects.append(obj)

	return obj

# Return an object to the pool
func return_object(obj):
	if obj == null or not is_instance_valid(obj):
		return

	# Remove from active tracking
	var index = active_objects.find(obj)
	if index >= 0:
		active_objects.remove(index)

	# Remove from scene tree
	if obj.get_parent():
		obj.get_parent().remove_child(obj)

	# Reset object state
	_reset_object(obj)

	# Deactivate the object
	obj.set_process(false)
	obj.set_physics_process(false)
	obj.visible = false

	# Return to available pool if we have space
	if available_objects.size() < max_pool_size:
		available_objects.append(obj)
	else:
		# Pool is full, destroy the object
		obj.queue_free()

# Reset method to properly reset crystal objects
func _reset_object(obj):
	if obj.has_method("reset_for_pool"):
		obj.reset_for_pool()
	else:
		# Fallback reset for crystal objects
		_reset_crystal_base(obj)

# Reset method for crystal objects (ChristalRigid.gd, CrystalSimple.gd)
func _reset_crystal_base(crystal):
	# Reset position and transform
	crystal.position = Vector2.ZERO
	crystal.rotation = 0
	crystal.scale = Vector2.ONE
	crystal.modulate = Color(1, 1, 1, 1)

	# Reset crystal state variables
	crystal.wasInit = false
	crystal.crystalType = Global.CrystalType.c5
	crystal.crystalValue = 5

	# Reset physics variables
	crystal.velocity = Vector2.ZERO
	crystal.gravity = 100.0  # Will be randomized in init
	crystal.initial_scatter_applied = false
	crystal.scatter_impulses_remaining = 1

	# Reset cached values
	crystal.cached_player_position = Vector2.ZERO
	crystal.cached_permanent_crystal_magnet = false
	crystal.cached_crystal_value_multiplier = 1.0
	crystal.player_position_update_counter = 0
	crystal.magnet_check_counter = 0
	crystal.cached_has_magnet_effect = false
	crystal.screen_check_counter = 0

	# Reset pool safety flag
	crystal.is_being_pooled = false

	# Reset magnet settings to defaults
	crystal.magnet_power = 50.0
	crystal.magnet_range = 400.0
	crystal.min_y_speed = 30.0
	crystal.y_slow_range = 350.0

	# Reset AnimatedSprite modulate
	if crystal.has_node("AnimatedSprite"):
		crystal.get_node("AnimatedSprite").modulate = Color(1.0, 1.0, 1.0, 1.0)

	# Important: Don't reconnect signals - they should remain connected from _ready()

# Get a crystal from the pool (without automatic initialization)
func get_crystal():
	var crystal = get_object()
	if crystal:
		# Set pool reference so crystal can return itself (if supported)
		if crystal.has_method("set_crystal_pool"):
			crystal.set_crystal_pool(self)
	return crystal

# Return a crystal to the pool (called from crystal's queue_free override)
func return_crystal(crystal):
	return_object(crystal)

# Get pool statistics
func get_stats():
	return {
		"available": available_objects.size(),
		"active": active_objects.size(),
		"total": available_objects.size() + active_objects.size()
	}

# Clean up the pool
func cleanup():
	# Free all available objects
	for obj in available_objects:
		if is_instance_valid(obj):
			obj.queue_free()
	available_objects.clear()

	# Note: Active objects should be returned to pool naturally
	# or will be cleaned up when their parent nodes are freed
	active_objects.clear()

func _exit_tree():
	cleanup()

# Create specialized pool instances for different crystal types
static func create_crystal_pool(parent_node_arg):
	var crystal_pool = load("res://scripts/CrystalPool.gd").new()
	var crystal_scene = preload("res://scenes/CrystalRigid.tscn")
	crystal_pool.initialize(crystal_scene, parent_node_arg, 15, 100)  # Start with 15, max 100
	return crystal_pool

# Legacy method for backward compatibility
static func create_global_instance(parent_node_arg):
	return create_crystal_pool(parent_node_arg)
